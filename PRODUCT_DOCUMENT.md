# 商品供应链系统产品需求文档 (PRD) - v2.0

## 📋 项目概述

### 🎯 项目背景

基于RuoYi-Vue-Plus 5.4.1框架，打造一个高效、智能、可扩展的B2B商品供应链平台。上游连接自营或第三方供应商，下游通过API或SaaS平台服务于分销商、零售商等客户，实现商品流、信息流、资金流的无缝整合与高效协同。

### 🚀 核心目标
- **提升供应链效率**: 自动化处理从采购、上架、下单到发货的全流程，降低人工成本
- **增强业务扩展性**: 通过插件化架构，灵活支持ERP、CRM、财务等增值服务，满足不同规模企业的需求
- **数据驱动决策**: 聚合供应链各环节数据，为采购、销售、库存管理提供数据支持
- **优化客户体验**: 提供稳定易用的API和SaaS平台，让客户可以轻松选品、下单和跟踪

### 📊 项目状态
- **开发分支**: supply-chain-dev
- **当前阶段**: 第一阶段 - 核心业务模块开发
- **已完成**: 商品分类管理 ✅
- **进行中**: 品牌管理、SPU/SKU管理

### 🎯 目标用户

| 角色 | 核心权限 |
| :--- | :--- |
| **超级管理员** | 拥有系统所有功能权限，包括租户管理、系统配置、插件管理等 |
| **租户管理员** | 管理所属租户下的所有业务功能，包括用户管理、角色分配、商品和订单管理等 |
| **供应商代表** | 商品管理（上架、编辑、下架）、库存更新、订单查看和发货处理 |
| **经销商代表** | 商品浏览与搜索、下单采购、订单跟踪、售后申请 |
| **财务专员** | 订单对账、发票管理、支付记录查看 |
| **运营专员** | 营销活动创建与管理、优惠券配置、数据报表查看 |

## 🏗️ 核心业务模块

### 3.1 商品管理 (Product Module) ✅

#### 已完成功能
- **商品分类管理** ✅
  - 多级分类树形结构
  - 分类编码和名称唯一性校验
  - 支持分类图标和图片
  - 完整的CRUD操作和权限控制

#### 待开发功能
- **品牌管理**: 独立的品牌管理功能
- **商品发布**: 支持SPU (Standard Product Unit) 和 SKU (Stock Keeping Unit) 模型
- **库存管理**: 实时更新商品库存，支持库存预警
- **商品审核**: 可配置的商品上架审核流程

### 3.2 订单管理 (Order Module) 🔄

- **下单流程**: 经销商通过平台或API接口下单，系统自动创建订单
- **订单状态管理**: 包括待付款、待发货、已发货、已完成、已取消、售后中等状态
- **订单查询**: 支持按多种条件（订单号、商品名称、收货人等）查询订单
- **发货管理**: 供应商填写物流信息，完成发货操作
- **售后服务**: 支持退款、退货、换货申请和处理流程

### 3.3 仓储与物流管理 (WMS & Logistics Module) 🔄

- **多仓库管理**: 支持多个仓库的独立管理，包括仓库信息、库区设置等
- **库存管理**: 实时库存查询、库存调拨、库存盘点等功能
- **入库管理**: 采购入库、退货入库、调拨入库等多种入库方式
- **出库管理**: 销售出库、调拨出库、报损出库等多种出库方式
- **物流跟踪**: 集成第三方物流API，实现物流信息的实时跟踪

## 🔌 辅助插件模块

### 4.1 ERP集成插件 🔄

- **库存同步**: 与外部ERP系统进行库存数据的双向同步
- **订单同步**: 订单信息自动推送到ERP系统进行后续处理
- **财务对接**: 销售数据、采购数据与财务系统的无缝对接

### 4.2 CRM客户管理插件 🔄

- **客户档案**: 完整的客户信息管理，包括基本信息、交易记录、信用评级等
- **客户分级**: 根据交易金额、频次等指标对客户进行分级管理
- **营销活动**: 针对不同客户群体推送个性化的营销活动

### 4.3 财务管理插件 🔄

- **自动对账**: 系统自动进行订单与支付的对账，减少人工操作
- **发票管理**: 支持电子发票的开具、查询和管理
- **财务报表**: 自动生成销售报表、利润分析、应收应付等财务报表

### 4.4 营销管理插件 🔄

- **优惠券系统**: 支持满减券、折扣券、新人券等多种优惠券类型
- **促销活动**: 限时折扣、满减活动、买赠活动等多种促销方式
- **会员体系**: 积分系统、会员等级、专享价格等会员权益管理

## 🔧 技术架构

### 5.1 技术栈
- **后端框架**: Spring Boot 3.4 + RuoYi-Vue-Plus 5.4.1
- **数据库**: MySQL 8.0+ (主库) + Redis (缓存)
- **认证授权**: Sa-Token + JWT
- **ORM框架**: MyBatis-Plus
- **前端框架**: Vue3 + TypeScript + ElementPlus
- **构建工具**: Maven 3.6+
- **JDK版本**: JDK 17/21

### 5.2 架构特性
- **多租户支持**: 基于TenantEntity实现数据隔离
- **微服务架构**: 模块化设计，支持独立部署
- **分布式缓存**: Redis集群支持
- **工作流引擎**: 支持复杂业务审批流程
- **代码生成**: 自动化CRUD代码生成

## 📊 数据模型设计

### 6.1 商品相关表
- **product_category**: 商品分类表 ✅
- **product_brand**: 品牌表 🔄
- **product_attribute**: 商品属性表 🔄
- **product_spu**: SPU表(商品标准单位) 🔄
- **product_sku**: SKU表(库存单位) 🔄
- **product_stock_log**: 库存变动记录表 🔄

### 6.2 订单相关表
- **order_main**: 订单主表 🔄
- **order_item**: 订单明细表 🔄
- **order_logistics**: 物流信息表 🔄
- **order_refund**: 退款记录表 🔄

### 6.3 仓储相关表
- **warehouse_info**: 仓库信息表 🔄
- **warehouse_area**: 库区信息表 🔄
- **stock_in**: 入库单表 🔄
- **stock_out**: 出库单表 🔄

## 🚀 开发里程碑

### 第一阶段：核心业务模块 (当前阶段)
- ✅ 商品分类管理
- 🔄 品牌管理
- 🔄 SPU/SKU管理
- 🔄 供应商管理
- 🔄 订单管理
- 🔄 仓储管理

### 第二阶段：辅助功能模块
- 🔄 财务对账模块
- 🔄 营销管理模块
- 🔄 物流管理模块

### 第三阶段：API接口与集成
- 🔄 RESTful API设计
- 🔄 第三方系统集成
- 🔄 API文档生成

### 第四阶段：数据分析与优化
- 🔄 数据大屏
- 🔄 智能推荐
- 🔄 性能优化

---

**文档版本**: v2.0  
**更新时间**: 2025-07-02  
**状态**: 开发中 - 第一阶段
