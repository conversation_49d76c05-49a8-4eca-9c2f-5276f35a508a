package org.dromara.product.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.product.domain.ProductCategory;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 商品分类视图对象 product_category
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductCategory.class)
public class ProductCategoryVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分类ID
     */
    @ExcelProperty(value = "分类ID")
    private Long categoryId;

    /**
     * 父分类ID
     */
    @ExcelProperty(value = "父分类ID")
    private Long parentId;

    /**
     * 分类名称
     */
    @ExcelProperty(value = "分类名称")
    private String categoryName;

    /**
     * 分类编码
     */
    @ExcelProperty(value = "分类编码")
    private String categoryCode;

    /**
     * 分类图标
     */
    @ExcelProperty(value = "分类图标")
    private String categoryIcon;

    /**
     * 分类图片
     */
    @ExcelProperty(value = "分类图片")
    private String categoryImage;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private Integer sortOrder;

    /**
     * 状态(0正常 1停用)
     */
    @ExcelProperty(value = "状态", converter = org.dromara.common.excel.convert.ExcelDictConvert.class)
    @org.dromara.common.excel.annotation.ExcelDictFormat(dictType = "sys_normal_disable")
    private String status;

    /**
     * 分类描述
     */
    @ExcelProperty(value = "分类描述")
    private String description;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 子分类列表
     */
    private List<ProductCategoryVo> children;

}
