package org.dromara.product.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.product.domain.bo.ProductAttributeBo;
import org.dromara.product.domain.vo.ProductAttributeVo;
import org.dromara.product.service.IProductAttributeService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商品属性
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/product/attribute")
public class ProductAttributeController extends BaseController {

    private final IProductAttributeService productAttributeService;

    /**
     * 查询商品属性列表
     */
    @SaCheckPermission("product:attribute:list")
    @GetMapping("/list")
    public TableDataInfo<ProductAttributeVo> list(ProductAttributeBo bo, PageQuery pageQuery) {
        return productAttributeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出商品属性列表
     */
    @SaCheckPermission("product:attribute:export")
    @Log(title = "商品属性", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProductAttributeBo bo, HttpServletResponse response) {
        List<ProductAttributeVo> list = productAttributeService.queryList(bo);
        ExcelUtil.exportExcel(list, "商品属性", ProductAttributeVo.class, response);
    }

    /**
     * 获取商品属性详细信息
     */
    @SaCheckPermission("product:attribute:query")
    @GetMapping("/{attributeId}")
    public R<ProductAttributeVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long attributeId) {
        return R.ok(productAttributeService.queryById(attributeId));
    }

    /**
     * 新增商品属性
     */
    @SaCheckPermission("product:attribute:add")
    @Log(title = "商品属性", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProductAttributeBo bo) {
        // 校验属性名称唯一性
        if (!productAttributeService.checkAttributeNameUnique(bo)) {
            return R.fail("新增商品属性'" + bo.getAttributeName() + "'失败，属性名称已存在");
        }
        // 校验属性编码唯一性
        if (bo.getAttributeCode() != null && !productAttributeService.checkAttributeCodeUnique(bo)) {
            return R.fail("新增商品属性'" + bo.getAttributeName() + "'失败，属性编码已存在");
        }
        return toAjax(productAttributeService.insertByBo(bo));
    }

    /**
     * 修改商品属性
     */
    @SaCheckPermission("product:attribute:edit")
    @Log(title = "商品属性", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProductAttributeBo bo) {
        // 校验属性名称唯一性
        if (!productAttributeService.checkAttributeNameUnique(bo)) {
            return R.fail("修改商品属性'" + bo.getAttributeName() + "'失败，属性名称已存在");
        }
        // 校验属性编码唯一性
        if (bo.getAttributeCode() != null && !productAttributeService.checkAttributeCodeUnique(bo)) {
            return R.fail("修改商品属性'" + bo.getAttributeName() + "'失败，属性编码已存在");
        }
        return toAjax(productAttributeService.updateByBo(bo));
    }

    /**
     * 删除商品属性
     */
    @SaCheckPermission("product:attribute:remove")
    @Log(title = "商品属性", businessType = BusinessType.DELETE)
    @DeleteMapping("/{attributeIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] attributeIds) {
        return toAjax(productAttributeService.deleteWithValidByIds(List.of(attributeIds), true));
    }

}
