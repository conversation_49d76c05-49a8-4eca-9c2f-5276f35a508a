package org.dromara.product.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.product.domain.bo.ProductAttributeBo;
import org.dromara.product.domain.vo.ProductAttributeVo;

import java.util.Collection;
import java.util.List;

/**
 * 商品属性Service接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface IProductAttributeService {

    /**
     * 查询商品属性
     */
    ProductAttributeVo queryById(Long attributeId);

    /**
     * 查询商品属性列表
     */
    TableDataInfo<ProductAttributeVo> queryPageList(ProductAttributeBo bo, PageQuery pageQuery);

    /**
     * 查询商品属性列表
     */
    List<ProductAttributeVo> queryList(ProductAttributeBo bo);

    /**
     * 新增商品属性
     */
    Boolean insertByBo(ProductAttributeBo bo);

    /**
     * 修改商品属性
     */
    Boolean updateByBo(ProductAttributeBo bo);

    /**
     * 校验并批量删除商品属性信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 校验属性编码是否唯一
     */
    Boolean checkAttributeCodeUnique(ProductAttributeBo bo);

    /**
     * 校验属性名称是否唯一
     */
    Boolean checkAttributeNameUnique(ProductAttributeBo bo);

}
