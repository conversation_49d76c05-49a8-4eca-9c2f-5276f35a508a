package org.dromara.product.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;

/**
 * 商品属性对象 product_attribute
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_attribute")
public class ProductAttribute extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 属性ID
     */
    @TableId(value = "attribute_id", type = IdType.ASSIGN_ID)
    private Long attributeId;

    /**
     * 属性名称
     */
    private String attributeName;

    /**
     * 属性编码
     */
    private String attributeCode;

    /**
     * 属性类型(1规格 2参数)
     */
    private String attributeType;

    /**
     * 输入类型(1手工录入 2从列表中选择 3多选)
     */
    private String inputType;

    /**
     * 可选值列表，用逗号分隔
     */
    private String inputList;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 是否必填(0否 1是)
     */
    private String isRequired;

    /**
     * 状态(0正常 1停用)
     */
    private String status;

    /**
     * 属性描述
     */
    private String description;

}
