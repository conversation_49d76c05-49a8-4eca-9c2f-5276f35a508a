package org.dromara.product.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.product.domain.ProductAttribute;
import org.dromara.product.domain.bo.ProductAttributeBo;
import org.dromara.product.domain.vo.ProductAttributeVo;
import org.dromara.product.mapper.ProductAttributeMapper;
import org.dromara.product.service.IProductAttributeService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 商品属性Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@RequiredArgsConstructor
@Service
public class ProductAttributeServiceImpl implements IProductAttributeService {

    private final ProductAttributeMapper baseMapper;

    /**
     * 查询商品属性
     */
    @Override
    public ProductAttributeVo queryById(Long attributeId){
        return baseMapper.selectVoById(attributeId);
    }

    /**
     * 查询商品属性列表
     */
    @Override
    public TableDataInfo<ProductAttributeVo> queryPageList(ProductAttributeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductAttribute> lqw = buildQueryWrapper(bo);
        Page<ProductAttributeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询商品属性列表
     */
    @Override
    public List<ProductAttributeVo> queryList(ProductAttributeBo bo) {
        LambdaQueryWrapper<ProductAttribute> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductAttribute> buildQueryWrapper(ProductAttributeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductAttribute> lqw = Wrappers.lambdaQueryWrapper();
        lqw.like(StringUtils.isNotBlank(bo.getAttributeName()), ProductAttribute::getAttributeName, bo.getAttributeName());
        lqw.eq(StringUtils.isNotBlank(bo.getAttributeCode()), ProductAttribute::getAttributeCode, bo.getAttributeCode());
        lqw.eq(StringUtils.isNotBlank(bo.getAttributeType()), ProductAttribute::getAttributeType, bo.getAttributeType());
        lqw.eq(StringUtils.isNotBlank(bo.getInputType()), ProductAttribute::getInputType, bo.getInputType());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ProductAttribute::getStatus, bo.getStatus());
        lqw.orderByAsc(ProductAttribute::getSortOrder);
        return lqw;
    }

    /**
     * 新增商品属性
     */
    @Override
    public Boolean insertByBo(ProductAttributeBo bo) {
        ProductAttribute add = MapstructUtils.convert(bo, ProductAttribute.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setAttributeId(add.getAttributeId());
        }
        return flag;
    }

    /**
     * 修改商品属性
     */
    @Override
    public Boolean updateByBo(ProductAttributeBo bo) {
        ProductAttribute update = MapstructUtils.convert(bo, ProductAttribute.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductAttribute entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除商品属性
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 校验属性编码是否唯一
     */
    @Override
    public Boolean checkAttributeCodeUnique(ProductAttributeBo bo) {
        boolean exist = baseMapper.exists(Wrappers.<ProductAttribute>lambdaQuery()
            .eq(ProductAttribute::getAttributeCode, bo.getAttributeCode())
            .ne(ObjectUtil.isNotNull(bo.getAttributeId()), ProductAttribute::getAttributeId, bo.getAttributeId()));
        return !exist;
    }

    /**
     * 校验属性名称是否唯一
     */
    @Override
    public Boolean checkAttributeNameUnique(ProductAttributeBo bo) {
        boolean exist = baseMapper.exists(Wrappers.<ProductAttribute>lambdaQuery()
            .eq(ProductAttribute::getAttributeName, bo.getAttributeName())
            .ne(ObjectUtil.isNotNull(bo.getAttributeId()), ProductAttribute::getAttributeId, bo.getAttributeId()));
        return !exist;
    }

}
