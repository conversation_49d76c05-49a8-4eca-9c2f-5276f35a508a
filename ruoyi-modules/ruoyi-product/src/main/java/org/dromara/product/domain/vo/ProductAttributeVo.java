package org.dromara.product.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.product.domain.ProductAttribute;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 商品属性视图对象 product_attribute
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductAttribute.class)
public class ProductAttributeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 属性ID
     */
    @ExcelProperty(value = "属性ID")
    private Long attributeId;

    /**
     * 属性名称
     */
    @ExcelProperty(value = "属性名称")
    private String attributeName;

    /**
     * 属性编码
     */
    @ExcelProperty(value = "属性编码")
    private String attributeCode;

    /**
     * 属性类型(1规格 2参数)
     */
    @ExcelProperty(value = "属性类型", converter = org.dromara.common.excel.convert.ExcelDictConvert.class)
    @org.dromara.common.excel.annotation.ExcelDictFormat(dictType = "product_attribute_type")
    private String attributeType;

    /**
     * 输入类型(1手工录入 2从列表中选择 3多选)
     */
    @ExcelProperty(value = "输入类型", converter = org.dromara.common.excel.convert.ExcelDictConvert.class)
    @org.dromara.common.excel.annotation.ExcelDictFormat(dictType = "product_input_type")
    private String inputType;

    /**
     * 可选值列表，用逗号分隔
     */
    @ExcelProperty(value = "可选值列表")
    private String inputList;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private Integer sortOrder;

    /**
     * 是否必填(0否 1是)
     */
    @ExcelProperty(value = "是否必填", converter = org.dromara.common.excel.convert.ExcelDictConvert.class)
    @org.dromara.common.excel.annotation.ExcelDictFormat(dictType = "sys_yes_no")
    private String isRequired;

    /**
     * 状态(0正常 1停用)
     */
    @ExcelProperty(value = "状态", converter = org.dromara.common.excel.convert.ExcelDictConvert.class)
    @org.dromara.common.excel.annotation.ExcelDictFormat(dictType = "sys_normal_disable")
    private String status;

    /**
     * 属性描述
     */
    @ExcelProperty(value = "属性描述")
    private String description;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

}
