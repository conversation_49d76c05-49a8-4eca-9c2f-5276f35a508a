package org.dromara.product.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;

/**
 * 商品品牌对象 product_brand
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_brand")
public class ProductBrand extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 品牌ID
     */
    @TableId(value = "brand_id", type = IdType.ASSIGN_ID)
    private Long brandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 品牌编码
     */
    private String brandCode;

    /**
     * 品牌LOGO
     */
    private String brandLogo;

    /**
     * 品牌官网
     */
    private String brandWebsite;

    /**
     * 品牌描述
     */
    private String brandDescription;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 状态(0正常 1停用)
     */
    private String status;

    /**
     * 是否推荐(0否 1是)
     */
    private String isRecommend;

}
