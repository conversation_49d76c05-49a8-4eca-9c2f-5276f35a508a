package org.dromara.product.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.product.domain.ProductAttribute;

/**
 * 商品属性业务对象 product_attribute
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductAttribute.class, reverseConvertGenerate = false)
public class ProductAttributeBo extends BaseEntity {

    /**
     * 属性ID
     */
    @NotNull(message = "属性ID不能为空", groups = { EditGroup.class })
    private Long attributeId;

    /**
     * 属性名称
     */
    @NotBlank(message = "属性名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String attributeName;

    /**
     * 属性编码
     */
    private String attributeCode;

    /**
     * 属性类型(1规格 2参数)
     */
    @NotBlank(message = "属性类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String attributeType;

    /**
     * 输入类型(1手工录入 2从列表中选择 3多选)
     */
    @NotBlank(message = "输入类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String inputType;

    /**
     * 可选值列表，用逗号分隔
     */
    private String inputList;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 是否必填(0否 1是)
     */
    private String isRequired;

    /**
     * 状态(0正常 1停用)
     */
    private String status;

    /**
     * 属性描述
     */
    private String description;

}
