import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:package',
      order: 2,
      title: '商品管理',
    },
    name: 'Product',
    path: '/product',
    children: [
      {
        name: 'ProductCategory',
        path: '/category',
        component: () => import('#/views/product/category/index.vue'),
        meta: {
          icon: 'lucide:folder-tree',
          title: '商品分类',
        },
      },
      {
        name: 'ProductBrand',
        path: '/brand',
        component: () => import('#/views/product/brand/index.vue'),
        meta: {
          icon: 'lucide:award',
          title: '商品品牌',
        },
      },
      {
        name: 'ProductAttribute',
        path: '/attribute',
        component: () => import('#/views/product/attribute/index.vue'),
        meta: {
          icon: 'lucide:settings',
          title: '商品属性',
        },
      },
    ],
  },
];

export default routes;
