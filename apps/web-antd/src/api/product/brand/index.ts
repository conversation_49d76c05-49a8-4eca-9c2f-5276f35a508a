import type { ProductBrand, ProductBrandForm, ProductBrandQuery } from './model';

import type { ID, PageQuery, PageResult } from '#/api/common';

import { requestClient } from '#/api/request';

enum Api {
  BrandList = '/product/brand/list',
  BrandInfo = '/product/brand',
  BrandAdd = '/product/brand',
  BrandEdit = '/product/brand',
  BrandRemove = '/product/brand',
  BrandExport = '/product/brand/export',
}

/**
 * 查询商品品牌列表
 */
export function brandList(params?: ProductBrandQuery & PageQuery) {
  return requestClient.get<PageResult<ProductBrand>>(Api.BrandList, { params });
}

/**
 * 获取商品品牌详细信息
 */
export function brandInfo(brandId: ID) {
  return requestClient.get<ProductBrand>(`${Api.BrandInfo}/${brandId}`);
}

/**
 * 新增商品品牌
 */
export function brandAdd(data: ProductBrandForm) {
  return requestClient.postWithMsg<void>(Api.BrandAdd, data);
}

/**
 * 修改商品品牌
 */
export function brandEdit(data: ProductBrandForm) {
  return requestClient.putWithMsg<void>(Api.BrandEdit, data);
}

/**
 * 删除商品品牌
 */
export function brandRemove(brandIds: ID | ID[]) {
  const ids = Array.isArray(brandIds) ? brandIds.join(',') : brandIds;
  return requestClient.deleteWithMsg<void>(`${Api.BrandRemove}/${ids}`);
}

/**
 * 导出商品品牌
 */
export function brandExport(params?: ProductBrandQuery) {
  return requestClient.post(Api.BrandExport, params, { responseType: 'blob' });
}
