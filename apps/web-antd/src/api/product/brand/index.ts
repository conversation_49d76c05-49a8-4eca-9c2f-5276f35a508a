import type { ProductBrand, ProductBrandForm, ProductBrandQuery } from './model';

import { requestClient } from '#/api/request';

enum Api {
  BrandList = '/product/brand/list',
  BrandInfo = '/product/brand',
  BrandAdd = '/product/brand',
  BrandEdit = '/product/brand',
  BrandRemove = '/product/brand',
  BrandExport = '/product/brand/export',
}

/**
 * 查询商品品牌列表
 */
export async function brandList(params?: ProductBrandQuery) {
  return requestClient.get<ProductBrand[]>(Api.BrandList, { params });
}

/**
 * 获取商品品牌详细信息
 */
export async function brandInfo(brandId: number) {
  return requestClient.get<ProductBrand>(`${Api.BrandInfo}/${brandId}`);
}

/**
 * 新增商品品牌
 */
export async function brandAdd(data: ProductBrandForm) {
  return requestClient.post(Api.BrandAdd, data);
}

/**
 * 修改商品品牌
 */
export async function brandEdit(data: ProductBrandForm) {
  return requestClient.put(Api.BrandEdit, data);
}

/**
 * 删除商品品牌
 */
export async function brandRemove(brandIds: number | number[]) {
  const ids = Array.isArray(brandIds) ? brandIds.join(',') : brandIds;
  return requestClient.delete(`${Api.BrandRemove}/${ids}`);
}

/**
 * 导出商品品牌
 */
export async function brandExport(params?: ProductBrandQuery) {
  return requestClient.post(Api.BrandExport, params, { responseType: 'blob' });
}
