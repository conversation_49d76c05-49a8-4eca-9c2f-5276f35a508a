/**
 * 商品品牌相关接口类型定义
 */

export interface ProductBrand {
  brandId?: number;
  brandName: string;
  brandCode?: string;
  brandLogo?: string;
  brandWebsite?: string;
  brandDescription?: string;
  sortOrder?: number;
  status?: string;
  isRecommend?: string;
  createTime?: string;
  updateTime?: string;
}

export interface ProductBrandQuery {
  brandName?: string;
  brandCode?: string;
  status?: string;
  isRecommend?: string;
}

export interface ProductBrandForm {
  brandId?: number;
  brandName: string;
  brandCode?: string;
  brandLogo?: string;
  brandWebsite?: string;
  brandDescription?: string;
  sortOrder?: number;
  status?: string;
  isRecommend?: string;
}
