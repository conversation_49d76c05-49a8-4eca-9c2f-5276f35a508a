/**
 * 商品属性相关接口类型定义
 */

export interface ProductAttribute {
  attributeId?: number;
  attributeName: string;
  attributeCode?: string;
  attributeType: string;
  inputType: string;
  inputList?: string;
  sortOrder?: number;
  isRequired?: string;
  status?: string;
  description?: string;
  createTime?: string;
  updateTime?: string;
}

export interface ProductAttributeQuery {
  attributeName?: string;
  attributeCode?: string;
  attributeType?: string;
  inputType?: string;
  status?: string;
}

export interface ProductAttributeForm {
  attributeId?: number;
  attributeName: string;
  attributeCode?: string;
  attributeType: string;
  inputType: string;
  inputList?: string;
  sortOrder?: number;
  isRequired?: string;
  status?: string;
  description?: string;
}
