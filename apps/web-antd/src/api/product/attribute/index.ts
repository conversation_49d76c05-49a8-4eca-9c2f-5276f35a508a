import type { ProductAttribute, ProductAttributeForm, ProductAttributeQuery } from './model';

import { requestClient } from '#/api/request';

enum Api {
  AttributeList = '/product/attribute/list',
  AttributeInfo = '/product/attribute',
  AttributeAdd = '/product/attribute',
  AttributeEdit = '/product/attribute',
  AttributeRemove = '/product/attribute',
  AttributeExport = '/product/attribute/export',
}

/**
 * 查询商品属性列表
 */
export async function attributeList(params?: ProductAttributeQuery) {
  return requestClient.get<ProductAttribute[]>(Api.AttributeList, { params });
}

/**
 * 获取商品属性详细信息
 */
export async function attributeInfo(attributeId: number) {
  return requestClient.get<ProductAttribute>(`${Api.AttributeInfo}/${attributeId}`);
}

/**
 * 新增商品属性
 */
export async function attributeAdd(data: ProductAttributeForm) {
  return requestClient.post(Api.AttributeAdd, data);
}

/**
 * 修改商品属性
 */
export async function attributeEdit(data: ProductAttributeForm) {
  return requestClient.put(Api.AttributeEdit, data);
}

/**
 * 删除商品属性
 */
export async function attributeRemove(attributeIds: number | number[]) {
  const ids = Array.isArray(attributeIds) ? attributeIds.join(',') : attributeIds;
  return requestClient.delete(`${Api.AttributeRemove}/${ids}`);
}

/**
 * 导出商品属性
 */
export async function attributeExport(params?: ProductAttributeQuery) {
  return requestClient.post(Api.AttributeExport, params, { responseType: 'blob' });
}
