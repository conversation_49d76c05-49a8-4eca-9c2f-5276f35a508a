import type { ProductAttribute, ProductAttributeForm, ProductAttributeQuery } from './model';

import type { ID, PageQuery, PageResult } from '#/api/common';

import { requestClient } from '#/api/request';

enum Api {
  AttributeList = '/product/attribute/list',
  AttributeInfo = '/product/attribute',
  AttributeAdd = '/product/attribute',
  AttributeEdit = '/product/attribute',
  AttributeRemove = '/product/attribute',
  AttributeExport = '/product/attribute/export',
}

/**
 * 查询商品属性列表
 */
export function attributeList(params?: ProductAttributeQuery & PageQuery) {
  return requestClient.get<PageResult<ProductAttribute>>(Api.AttributeList, { params });
}

/**
 * 获取商品属性详细信息
 */
export function attributeInfo(attributeId: ID) {
  return requestClient.get<ProductAttribute>(`${Api.AttributeInfo}/${attributeId}`);
}

/**
 * 新增商品属性
 */
export function attributeAdd(data: ProductAttributeForm) {
  return requestClient.postWithMsg<void>(Api.AttributeAdd, data);
}

/**
 * 修改商品属性
 */
export function attributeEdit(data: ProductAttributeForm) {
  return requestClient.putWithMsg<void>(Api.AttributeEdit, data);
}

/**
 * 删除商品属性
 */
export function attributeRemove(attributeIds: ID | ID[]) {
  const ids = Array.isArray(attributeIds) ? attributeIds.join(',') : attributeIds;
  return requestClient.deleteWithMsg<void>(`${Api.AttributeRemove}/${ids}`);
}

/**
 * 导出商品属性
 */
export function attributeExport(params?: ProductAttributeQuery) {
  return requestClient.post(Api.AttributeExport, params, { responseType: 'blob' });
}
