import type { ProductCategory, ProductCategoryForm, ProductCategoryQuery } from './model';

import { requestClient } from '#/api/request';

enum Api {
  CategoryList = '/product/category/list',
  CategoryTree = '/product/category/tree',
  CategoryInfo = '/product/category',
  CategoryAdd = '/product/category',
  CategoryEdit = '/product/category',
  CategoryRemove = '/product/category',
  CategoryExport = '/product/category/export',
}

/**
 * 查询商品分类列表
 */
export async function categoryList(params?: ProductCategoryQuery) {
  return requestClient.get<ProductCategory[]>(Api.CategoryList, { params });
}

/**
 * 查询商品分类树形结构
 */
export async function categoryTree(params?: ProductCategoryQuery) {
  return requestClient.get<ProductCategory[]>(Api.CategoryTree, { params });
}

/**
 * 获取商品分类详细信息
 */
export async function categoryInfo(categoryId: number) {
  return requestClient.get<ProductCategory>(`${Api.CategoryInfo}/${categoryId}`);
}

/**
 * 新增商品分类
 */
export async function categoryAdd(data: ProductCategoryForm) {
  return requestClient.post(Api.CategoryAdd, data);
}

/**
 * 修改商品分类
 */
export async function categoryEdit(data: ProductCategoryForm) {
  return requestClient.put(Api.CategoryEdit, data);
}

/**
 * 删除商品分类
 */
export async function categoryRemove(categoryIds: number | number[]) {
  const ids = Array.isArray(categoryIds) ? categoryIds.join(',') : categoryIds;
  return requestClient.delete(`${Api.CategoryRemove}/${ids}`);
}

/**
 * 导出商品分类
 */
export async function categoryExport(params?: ProductCategoryQuery) {
  return requestClient.post(Api.CategoryExport, params, { responseType: 'blob' });
}
