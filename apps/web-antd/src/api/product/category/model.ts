/**
 * 商品分类相关接口类型定义
 */

export interface ProductCategory {
  categoryId?: number;
  parentId?: number;
  categoryName: string;
  categoryCode?: string;
  categoryIcon?: string;
  categoryDescription?: string;
  sortOrder?: number;
  status?: string;
  createTime?: string;
  updateTime?: string;
  children?: ProductCategory[];
}

export interface ProductCategoryQuery {
  categoryName?: string;
  categoryCode?: string;
  status?: string;
  parentId?: number;
}

export interface ProductCategoryForm {
  categoryId?: number;
  parentId?: number;
  categoryName: string;
  categoryCode?: string;
  categoryIcon?: string;
  categoryDescription?: string;
  sortOrder?: number;
  status?: string;
}
