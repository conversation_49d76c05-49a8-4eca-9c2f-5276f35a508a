import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { DictEnum } from '@vben/constants';
import { getPopupContainer } from '@vben/utils';

import { z } from '#/adapter/form';
import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'brandName',
    label: '品牌名称',
  },
  {
    component: 'Input',
    fieldName: 'brandCode',
    label: '品牌编码',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions(DictEnum.SYS_NORMAL_DISABLE),
    },
    fieldName: 'status',
    label: '状态',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions(DictEnum.SYS_YES_NO),
    },
    fieldName: 'isRecommend',
    label: '是否推荐',
  },
];

export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 50,
  },
  {
    field: 'brandName',
    title: '品牌名称',
    minWidth: 150,
  },
  {
    field: 'brandCode',
    title: '品牌编码',
    width: 120,
  },
  {
    field: 'brandLogo',
    title: '品牌LOGO',
    width: 100,
    slots: {
      default: ({ row }) => {
        return row.brandLogo ? (
          <img src={row.brandLogo} alt="logo" style={{ width: '32px', height: '32px', objectFit: 'cover' }} />
        ) : (
          '-'
        );
      },
    },
  },
  {
    field: 'brandWebsite',
    title: '品牌官网',
    width: 200,
    slots: {
      default: ({ row }) => {
        return row.brandWebsite ? (
          <a href={row.brandWebsite} target="_blank" rel="noopener noreferrer">
            {row.brandWebsite}
          </a>
        ) : (
          '-'
        );
      },
    },
  },
  {
    field: 'sortOrder',
    title: '排序',
    width: 80,
  },
  {
    field: 'status',
    title: '状态',
    width: 100,
    slots: {
      default: ({ row }) => {
        return renderDict(row.status, DictEnum.SYS_NORMAL_DISABLE);
      },
    },
  },
  {
    field: 'isRecommend',
    title: '是否推荐',
    width: 100,
    slots: {
      default: ({ row }) => {
        return renderDict(row.isRecommend, DictEnum.SYS_YES_NO);
      },
    },
  },
  {
    field: 'createTime',
    title: '创建时间',
    width: 180,
  },
];

export const formSchema: FormSchemaGetter = (params) => {
  const { isEdit } = params || {};
  return [
    {
      component: 'Input',
      fieldName: 'brandId',
      label: '品牌ID',
      dependencies: {
        show: false,
        triggerFields: [],
      },
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入品牌名称',
      },
      fieldName: 'brandName',
      label: '品牌名称',
      rules: z.string().min(1, { message: '请输入品牌名称' }),
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入品牌编码',
      },
      fieldName: 'brandCode',
      label: '品牌编码',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入品牌LOGO地址',
      },
      fieldName: 'brandLogo',
      label: '品牌LOGO',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入品牌官网',
      },
      fieldName: 'brandWebsite',
      label: '品牌官网',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        placeholder: '请输入排序',
      },
      fieldName: 'sortOrder',
      label: '排序',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        options: getDictOptions(DictEnum.SYS_NORMAL_DISABLE),
      },
      fieldName: 'status',
      label: '状态',
      rules: z.string().min(1, { message: '请选择状态' }),
    },
    {
      component: 'RadioGroup',
      componentProps: {
        options: getDictOptions(DictEnum.SYS_YES_NO),
      },
      fieldName: 'isRecommend',
      label: '是否推荐',
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入品牌描述',
        rows: 3,
      },
      fieldName: 'brandDescription',
      label: '品牌描述',
    },
  ];
};
