<template>
  <VbenDrawer
    v-model:open="drawerState.visible"
    :loading="drawerState.loading"
    :title="drawerState.title"
    class="w-full max-w-2xl"
    @close="handleClose"
  >
    <VbenForm
      ref="formRef"
      :schema="formSchema"
      @submit="handleSubmit"
    />

    <template #footer>
      <div class="flex justify-end space-x-2">
        <Button @click="handleClose">取消</Button>
        <Button
          :loading="drawerState.loading"
          type="primary"
          @click="handleSubmit"
        >
          确定
        </Button>
      </div>
    </template>
  </VbenDrawer>
</template>

<script setup lang="ts">
import type { VbenFormInstance } from '@vben/common-ui';

import { nextTick, reactive, ref } from 'vue';

import { VbenDrawer, VbenForm } from '@vben/common-ui';

import { Button, message } from 'ant-design-vue';

import { brandAdd, brandEdit, brandInfo } from '#/api/product/brand';
import type { ProductBrand, ProductBrandForm } from '#/api/product/brand/model';

import { formSchema } from './data';

const emit = defineEmits<{
  success: [];
}>();

const formRef = ref<VbenFormInstance>();

const drawerState = reactive({
  visible: false,
  loading: false,
  title: '',
  isEdit: false,
  record: {} as ProductBrand,
});

async function handleOpen(record?: ProductBrand) {
  drawerState.visible = true;
  drawerState.isEdit = !!record?.brandId;
  drawerState.title = drawerState.isEdit ? '编辑品牌' : '新增品牌';
  drawerState.record = record || {};

  await nextTick();

  if (drawerState.isEdit && record?.brandId) {
    drawerState.loading = true;
    try {
      const data = await brandInfo(record.brandId);
      await formRef.value?.setValues(data);
    } catch (error) {
      console.error('获取品牌信息失败:', error);
    } finally {
      drawerState.loading = false;
    }
  } else {
    await formRef.value?.resetForm();
    // 设置默认值
    await formRef.value?.setValues({
      status: '0',
      isRecommend: '0',
      sortOrder: 0,
    });
  }
}

async function handleSubmit() {
  try {
    drawerState.loading = true;
    const values = await formRef.value?.submitForm();
    
    if (drawerState.isEdit) {
      await brandEdit(values as ProductBrandForm);
      message.success('编辑成功');
    } else {
      await brandAdd(values as ProductBrandForm);
      message.success('新增成功');
    }
    
    emit('success');
    handleClose();
  } catch (error) {
    console.error('提交失败:', error);
  } finally {
    drawerState.loading = false;
  }
}

function handleClose() {
  drawerState.visible = false;
  drawerState.loading = false;
  formRef.value?.resetForm();
}

defineExpose({
  open: handleOpen,
});
</script>
