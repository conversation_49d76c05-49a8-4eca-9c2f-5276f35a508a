<template>
  <VbenDrawer
    v-model:open="drawerState.visible"
    :loading="drawerState.loading"
    :title="drawerState.title"
    class="w-full max-w-2xl"
    @close="handleClose"
  >
    <VbenForm
      ref="formRef"
      :schema="formSchema"
      @submit="handleSubmit"
    />

    <template #footer>
      <div class="flex justify-end space-x-2">
        <Button @click="handleClose">取消</Button>
        <Button
          :loading="drawerState.loading"
          type="primary"
          @click="handleSubmit"
        >
          确定
        </Button>
      </div>
    </template>
  </VbenDrawer>
</template>

<script setup lang="ts">
import type { VbenFormInstance } from '@vben/common-ui';

import { nextTick, reactive, ref } from 'vue';

import { VbenDrawer, VbenForm } from '@vben/common-ui';

import { Button, message } from 'ant-design-vue';

import { attributeAdd, attributeEdit, attributeInfo } from '#/api/product/attribute';
import type { ProductAttribute, ProductAttributeForm } from '#/api/product/attribute/model';

import { formSchema } from './data';

const emit = defineEmits<{
  success: [];
}>();

const formRef = ref<VbenFormInstance>();

const drawerState = reactive({
  visible: false,
  loading: false,
  title: '',
  isEdit: false,
  record: {} as ProductAttribute,
});

async function handleOpen(record?: ProductAttribute) {
  drawerState.visible = true;
  drawerState.isEdit = !!record?.attributeId;
  drawerState.title = drawerState.isEdit ? '编辑属性' : '新增属性';
  drawerState.record = record || {};

  await nextTick();

  if (drawerState.isEdit && record?.attributeId) {
    drawerState.loading = true;
    try {
      const data = await attributeInfo(record.attributeId);
      await formRef.value?.setValues(data);
    } catch (error) {
      console.error('获取属性信息失败:', error);
    } finally {
      drawerState.loading = false;
    }
  } else {
    await formRef.value?.resetForm();
    // 设置默认值
    await formRef.value?.setValues({
      attributeType: '1',
      inputType: '1',
      isRequired: '0',
      status: '0',
      sortOrder: 0,
    });
  }
}

async function handleSubmit() {
  try {
    drawerState.loading = true;
    const values = await formRef.value?.submitForm();
    
    if (drawerState.isEdit) {
      await attributeEdit(values as ProductAttributeForm);
      message.success('编辑成功');
    } else {
      await attributeAdd(values as ProductAttributeForm);
      message.success('新增成功');
    }
    
    emit('success');
    handleClose();
  } catch (error) {
    console.error('提交失败:', error);
  } finally {
    drawerState.loading = false;
  }
}

function handleClose() {
  drawerState.visible = false;
  drawerState.loading = false;
  formRef.value?.resetForm();
}

defineExpose({
  open: handleOpen,
});
</script>
