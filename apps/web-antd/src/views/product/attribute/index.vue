<template>
  <Page>
    <VbenVxeGrid ref="gridRef" v-bind="gridOptions">
      <template #toolbar-actions>
        <Button
          type="primary"
          @click="handleAdd"
        >
          <template #icon>
            <PlusOutlined />
          </template>
          新增
        </Button>
        <Button
          :disabled="!hasSelected"
          danger
          type="primary"
          @click="handleBatchDelete"
        >
          <template #icon>
            <DeleteOutlined />
          </template>
          删除
        </Button>
        <Button @click="handleExport">
          <template #icon>
            <ExportOutlined />
          </template>
          导出
        </Button>
      </template>

      <template #action="{ row }">
        <Space>
          <Button
            size="small"
            type="link"
            @click="handleEdit(row)"
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除该属性吗？"
            @confirm="handleDelete(row)"
          >
            <Button
              danger
              size="small"
              type="link"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      </template>
    </VbenVxeGrid>

    <AttributeDrawer
      ref="drawerRef"
      @success="handleSuccess"
    />
  </Page>
</template>

<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeGridInstance, VxeGridProps } from '#/adapter/vxe-table';
import type { ProductAttribute } from '#/api/product/attribute/model';

import { computed, ref } from 'vue';

import { Page } from '@vben/common-ui';

import {
  Button,
  message,
  Popconfirm,
  Space,
} from 'ant-design-vue';
import {
  DeleteOutlined,
  ExportOutlined,
  PlusOutlined,
} from '@ant-design/icons-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { attributeExport, attributeList, attributeRemove } from '#/api/product/attribute';

import { columns, querySchema } from './data';
import AttributeDrawer from './attribute-drawer.vue';

const gridRef = ref<VxeGridInstance>();
const drawerRef = ref();

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
};

const gridOptions: VxeGridProps = {
  columns: [
    ...columns,
    {
      field: 'action',
      fixed: 'right',
      title: '操作',
      width: 150,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  keepSource: true,
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        const resp = await attributeList({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
        return {
          total: resp.total,
          rows: resp.rows,
        };
      },
    },
  },
  toolbarConfig: {
    refresh: true,
    zoom: true,
    custom: true,
  },
};

const [VbenVxeGrid, tableApi] = useVbenVxeGrid({ formOptions, gridOptions });

const hasSelected = computed(() => {
  return tableApi.grid.getCheckboxRecords().length > 0;
});

function handleAdd() {
  drawerRef.value?.open();
}

function handleEdit(record: ProductAttribute) {
  drawerRef.value?.open(record);
}

async function handleDelete(record: ProductAttribute) {
  try {
    await attributeRemove(record.attributeId!);
    message.success('删除成功');
    await tableApi.grid.commitProxy('query');
  } catch (error) {
    console.error('删除失败:', error);
  }
}

async function handleBatchDelete() {
  const records = tableApi.grid.getCheckboxRecords();
  if (records.length === 0) {
    message.warning('请选择要删除的数据');
    return;
  }

  try {
    const ids = records.map(record => record.attributeId);
    await attributeRemove(ids);
    message.success('删除成功');
    await tableApi.grid.commitProxy('query');
  } catch (error) {
    console.error('批量删除失败:', error);
  }
}

async function handleExport() {
  try {
    const formValues = tableApi.form.getValues();
    await attributeExport(formValues);
    message.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
  }
}

function handleSuccess() {
  tableApi.grid.commitProxy('query');
}
</script>
