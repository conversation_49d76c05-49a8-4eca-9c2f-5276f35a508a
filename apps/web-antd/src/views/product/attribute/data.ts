import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { DictEnum } from '@vben/constants';
import { getPopupContainer } from '@vben/utils';

import { z } from '#/adapter/form';
import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

// 商品属性类型字典
const PRODUCT_ATTRIBUTE_TYPE = 'product_attribute_type';
// 商品输入类型字典
const PRODUCT_INPUT_TYPE = 'product_input_type';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'attributeName',
    label: '属性名称',
  },
  {
    component: 'Input',
    fieldName: 'attributeCode',
    label: '属性编码',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions(PRODUCT_ATTRIBUTE_TYPE),
    },
    fieldName: 'attributeType',
    label: '属性类型',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions(DictEnum.SYS_NORMAL_DISABLE),
    },
    fieldName: 'status',
    label: '状态',
  },
];

export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 50,
  },
  {
    field: 'attributeName',
    title: '属性名称',
    minWidth: 150,
  },
  {
    field: 'attributeCode',
    title: '属性编码',
    width: 120,
  },
  {
    field: 'attributeType',
    title: '属性类型',
    width: 100,
    slots: {
      default: ({ row }) => {
        return renderDict(row.attributeType, PRODUCT_ATTRIBUTE_TYPE);
      },
    },
  },
  {
    field: 'inputType',
    title: '输入类型',
    width: 120,
    slots: {
      default: ({ row }) => {
        return renderDict(row.inputType, PRODUCT_INPUT_TYPE);
      },
    },
  },
  {
    field: 'inputList',
    title: '可选值',
    width: 200,
    slots: {
      default: ({ row }) => {
        return row.inputList || '-';
      },
    },
  },
  {
    field: 'sortOrder',
    title: '排序',
    width: 80,
  },
  {
    field: 'isRequired',
    title: '是否必填',
    width: 100,
    slots: {
      default: ({ row }) => {
        return renderDict(row.isRequired, DictEnum.SYS_YES_NO);
      },
    },
  },
  {
    field: 'status',
    title: '状态',
    width: 100,
    slots: {
      default: ({ row }) => {
        return renderDict(row.status, DictEnum.SYS_NORMAL_DISABLE);
      },
    },
  },
  {
    field: 'createTime',
    title: '创建时间',
    width: 180,
  },
];

export const formSchema: FormSchemaGetter = (params) => {
  const { isEdit } = params || {};
  return [
    {
      component: 'Input',
      fieldName: 'attributeId',
      label: '属性ID',
      dependencies: {
        show: false,
        triggerFields: [],
      },
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入属性名称',
      },
      fieldName: 'attributeName',
      label: '属性名称',
      rules: z.string().min(1, { message: '请输入属性名称' }),
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入属性编码',
      },
      fieldName: 'attributeCode',
      label: '属性编码',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        options: getDictOptions(PRODUCT_ATTRIBUTE_TYPE),
      },
      fieldName: 'attributeType',
      label: '属性类型',
      rules: z.string().min(1, { message: '请选择属性类型' }),
    },
    {
      component: 'RadioGroup',
      componentProps: {
        options: getDictOptions(PRODUCT_INPUT_TYPE),
      },
      fieldName: 'inputType',
      label: '输入类型',
      rules: z.string().min(1, { message: '请选择输入类型' }),
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入可选值列表，用逗号分隔',
        rows: 3,
      },
      fieldName: 'inputList',
      label: '可选值列表',
      dependencies: {
        show: ({ values }) => values.inputType === '2' || values.inputType === '3',
        triggerFields: ['inputType'],
      },
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        placeholder: '请输入排序',
      },
      fieldName: 'sortOrder',
      label: '排序',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        options: getDictOptions(DictEnum.SYS_YES_NO),
      },
      fieldName: 'isRequired',
      label: '是否必填',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        options: getDictOptions(DictEnum.SYS_NORMAL_DISABLE),
      },
      fieldName: 'status',
      label: '状态',
      rules: z.string().min(1, { message: '请选择状态' }),
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入属性描述',
        rows: 3,
      },
      fieldName: 'description',
      label: '属性描述',
    },
  ];
};
