import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { DictEnum } from '@vben/constants';
import { VbenIcon } from '@vben/icons';
import { getPopupContainer } from '@vben/utils';

import { z } from '#/adapter/form';
import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'categoryName',
    label: '分类名称',
  },
  {
    component: 'Input',
    fieldName: 'categoryCode',
    label: '分类编码',
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions(DictEnum.SYS_NORMAL_DISABLE),
    },
    fieldName: 'status',
    label: '状态',
  },
];

export const columns: VxeGridProps['columns'] = [
  {
    field: 'categoryName',
    title: '分类名称',
    treeNode: true,
    minWidth: 200,
  },
  {
    field: 'categoryCode',
    title: '分类编码',
    width: 150,
  },
  {
    field: 'categoryIcon',
    title: '分类图标',
    width: 100,
    slots: {
      default: ({ row }) => {
        return row.categoryIcon ? (
          <span class={'flex justify-center'}>
            <VbenIcon icon={row.categoryIcon} />
          </span>
        ) : (
          <span>-</span>
        );
      },
    },
  },
  {
    field: 'sortOrder',
    title: '排序',
    width: 80,
  },
  {
    field: 'status',
    title: '状态',
    width: 100,
    slots: {
      default: ({ row }) => {
        return renderDict(row.status, DictEnum.SYS_NORMAL_DISABLE);
      },
    },
  },
  {
    field: 'createTime',
    title: '创建时间',
    width: 180,
  },
];

export const formSchema: FormSchemaGetter = (params) => {
  const { isEdit } = params || {};
  return [
    {
      component: 'Input',
      fieldName: 'categoryId',
      label: '分类ID',
      dependencies: {
        show: false,
        triggerFields: [],
      },
    },
    {
      component: 'TreeSelect',
      componentProps: {
        allowClear: true,
        getPopupContainer,
        placeholder: '请选择上级分类',
        showSearch: true,
        treeDefaultExpandAll: true,
        treeNodeFilterProp: 'title',
      },
      fieldName: 'parentId',
      label: '上级分类',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入分类名称',
      },
      fieldName: 'categoryName',
      label: '分类名称',
      rules: z.string().min(1, { message: '请输入分类名称' }),
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入分类编码',
      },
      fieldName: 'categoryCode',
      label: '分类编码',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入分类图标',
      },
      fieldName: 'categoryIcon',
      label: '分类图标',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        placeholder: '请输入排序',
      },
      fieldName: 'sortOrder',
      label: '排序',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        options: getDictOptions(DictEnum.SYS_NORMAL_DISABLE),
      },
      fieldName: 'status',
      label: '状态',
      rules: z.string().min(1, { message: '请选择状态' }),
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入分类描述',
        rows: 3,
      },
      fieldName: 'categoryDescription',
      label: '分类描述',
    },
  ];
};
