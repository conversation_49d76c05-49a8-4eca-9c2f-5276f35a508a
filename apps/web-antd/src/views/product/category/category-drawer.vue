<template>
  <VbenDrawer
    v-model:open="drawerState.visible"
    :loading="drawerState.loading"
    :title="drawerState.title"
    class="w-full max-w-2xl"
    @close="handleClose"
  >
    <VbenForm
      ref="formRef"
      :schema="formSchema"
      @submit="handleSubmit"
    />

    <template #footer>
      <div class="flex justify-end space-x-2">
        <Button @click="handleClose">取消</Button>
        <Button
          :loading="drawerState.loading"
          type="primary"
          @click="handleSubmit"
        >
          确定
        </Button>
      </div>
    </template>
  </VbenDrawer>
</template>

<script setup lang="ts">
import type { VbenFormInstance } from '@vben/common-ui';

import { computed, nextTick, reactive, ref } from 'vue';

import { VbenDrawer, VbenForm } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { Button, message } from 'ant-design-vue';

import { categoryAdd, categoryEdit, categoryInfo, categoryTree } from '#/api/product/category';
import type { ProductCategory, ProductCategoryForm } from '#/api/product/category/model';

import { formSchema } from './data';

interface Props {
  categoryTreeData?: ProductCategory[];
}

const props = withDefaults(defineProps<Props>(), {
  categoryTreeData: () => [],
});

const emit = defineEmits<{
  success: [];
}>();

const formRef = ref<VbenFormInstance>();

const drawerState = reactive({
  visible: false,
  loading: false,
  title: '',
  isEdit: false,
  record: {} as ProductCategory,
});

const formSchemaComputed = computed(() => {
  const schema = formSchema({ isEdit: drawerState.isEdit });
  
  // 设置上级分类的选项数据
  const parentIdField = schema.find(item => item.fieldName === 'parentId');
  if (parentIdField) {
    parentIdField.componentProps = {
      ...parentIdField.componentProps,
      treeData: buildTreeData(props.categoryTreeData, drawerState.record.categoryId),
    };
  }
  
  return schema;
});

// 构建树形数据，排除当前节点及其子节点
function buildTreeData(data: ProductCategory[], excludeId?: number): any[] {
  return data
    .filter(item => item.categoryId !== excludeId)
    .map(item => ({
      title: item.categoryName,
      value: item.categoryId,
      key: item.categoryId,
      children: item.children ? buildTreeData(item.children, excludeId) : undefined,
    }));
}

async function handleOpen(record?: ProductCategory) {
  drawerState.visible = true;
  drawerState.isEdit = !!record?.categoryId;
  drawerState.title = drawerState.isEdit ? '编辑分类' : '新增分类';
  drawerState.record = record || {};

  await nextTick();

  if (drawerState.isEdit && record?.categoryId) {
    drawerState.loading = true;
    try {
      const data = await categoryInfo(record.categoryId);
      await formRef.value?.setValues(data);
    } catch (error) {
      console.error('获取分类信息失败:', error);
    } finally {
      drawerState.loading = false;
    }
  } else {
    await formRef.value?.resetForm();
    // 如果有传入父级ID，设置默认值
    if (record?.categoryId) {
      await formRef.value?.setValues({ parentId: record.categoryId });
    }
  }
}

async function handleSubmit() {
  try {
    drawerState.loading = true;
    const values = await formRef.value?.submitForm();
    
    if (drawerState.isEdit) {
      await categoryEdit(values as ProductCategoryForm);
      message.success('编辑成功');
    } else {
      await categoryAdd(values as ProductCategoryForm);
      message.success('新增成功');
    }
    
    emit('success');
    handleClose();
  } catch (error) {
    console.error('提交失败:', error);
  } finally {
    drawerState.loading = false;
  }
}

function handleClose() {
  drawerState.visible = false;
  drawerState.loading = false;
  formRef.value?.resetForm();
}

defineExpose({
  open: handleOpen,
});
</script>
