<template>
  <Page>
    <VbenVxeGrid ref="gridRef" v-bind="gridOptions">
      <template #toolbar-actions>
        <Button
          type="primary"
          @click="handleAdd"
        >
          <template #icon>
            <PlusOutlined />
          </template>
          新增
        </Button>
        <Button
          :disabled="!hasSelected"
          danger
          type="primary"
          @click="handleBatchDelete"
        >
          <template #icon>
            <DeleteOutlined />
          </template>
          删除
        </Button>
        <Button @click="handleExport">
          <template #icon>
            <ExportOutlined />
          </template>
          导出
        </Button>
      </template>

      <template #categoryIcon="{ row }">
        <span v-if="row.categoryIcon" class="flex justify-center">
          <VbenIcon :icon="row.categoryIcon" />
        </span>
        <span v-else>-</span>
      </template>

      <template #status="{ row }">
        <DictTag :dict-enum="DictEnum.SYS_NORMAL_DISABLE" :value="row.status" />
      </template>

      <template #action="{ row }">
        <Space>
          <Button
            size="small"
            type="link"
            @click="handleAdd(row)"
          >
            新增
          </Button>
          <Button
            size="small"
            type="link"
            @click="handleEdit(row)"
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除该分类吗？"
            @confirm="handleDelete(row)"
          >
            <Button
              danger
              size="small"
              type="link"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      </template>
    </VbenVxeGrid>

    <CategoryDrawer
      ref="drawerRef"
      :category-tree-data="categoryTreeData"
      @success="handleSuccess"
    />
  </Page>
</template>

<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeGridInstance, VxeGridProps } from '#/adapter/vxe-table';
import type { ProductCategory } from '#/api/product/category/model';

import { computed, nextTick, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { DictEnum } from '@vben/constants';
import { VbenIcon } from '@vben/icons';
import { eachTree } from '@vben/utils';

import { DictTag } from '#/components/dict';

import {
  Button,
  message,
  Popconfirm,
  Space,
} from 'ant-design-vue';
import {
  DeleteOutlined,
  ExportOutlined,
  PlusOutlined,
} from '@ant-design/icons-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { categoryExport, categoryRemove, categoryTree } from '#/api/product/category';

import { columns, querySchema } from './data';
import CategoryDrawer from './category-drawer.vue';

const gridRef = ref<VxeGridInstance>();
const drawerRef = ref();

const categoryTreeData = ref<ProductCategory[]>([]);

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
};

const gridOptions: VxeGridProps = {
  columns: [
    ...columns,
    {
      field: 'action',
      fixed: 'right',
      title: '操作',
      width: 200,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async (_, formValues = {}) => {
        const resp = await categoryTree({
          ...formValues,
        });
        categoryTreeData.value = resp;
        return { rows: resp };
      },
      // 默认展开全部
      querySuccess: () => {
        eachTree(tableApi.grid.getData(), (item) => (item.expand = true));
        nextTick(() => {
          tableApi.grid.setAllTreeExpand(true);
        });
      },
    },
  },
  toolbarConfig: {
    refresh: true,
    zoom: true,
    custom: true,
  },
  treeConfig: {
    childrenField: 'children',
    expandAll: true,
    indent: 20,
    line: true,
    rowField: 'categoryId',
  },
};

const [VbenVxeGrid, tableApi] = useVbenVxeGrid({ formOptions, gridOptions });

const hasSelected = computed(() => {
  return tableApi.grid.getCheckboxRecords().length > 0;
});

function handleAdd(record?: ProductCategory) {
  drawerRef.value?.open(record);
}

function handleEdit(record: ProductCategory) {
  drawerRef.value?.open(record);
}

async function handleDelete(record: ProductCategory) {
  try {
    await categoryRemove(record.categoryId!);
    message.success('删除成功');
    await tableApi.grid.commitProxy('query');
  } catch (error) {
    console.error('删除失败:', error);
  }
}

async function handleBatchDelete() {
  const records = tableApi.grid.getCheckboxRecords();
  if (records.length === 0) {
    message.warning('请选择要删除的数据');
    return;
  }

  try {
    const ids = records.map(record => record.categoryId);
    await categoryRemove(ids);
    message.success('删除成功');
    await tableApi.grid.commitProxy('query');
  } catch (error) {
    console.error('批量删除失败:', error);
  }
}

async function handleExport() {
  try {
    const formValues = tableApi.form.getValues();
    await categoryExport(formValues);
    message.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
  }
}

function handleSuccess() {
  tableApi.grid.commitProxy('query');
}
</script>
