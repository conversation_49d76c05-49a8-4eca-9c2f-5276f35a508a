-- ----------------------------
-- 商品属性表
-- ----------------------------
DROP TABLE IF EXISTS `product_attribute`;
CREATE TABLE `product_attribute` (
  `attribute_id` bigint NOT NULL COMMENT '属性ID',
  `tenant_id` varchar(20) DEFAULT '000000' COMMENT '租户编号',
  `attribute_name` varchar(100) NOT NULL COMMENT '属性名称',
  `attribute_code` varchar(50) DEFAULT NULL COMMENT '属性编码',
  `attribute_type` char(1) NOT NULL DEFAULT '1' COMMENT '属性类型(1规格 2参数)',
  `input_type` char(1) NOT NULL DEFAULT '1' COMMENT '输入类型(1手工录入 2从列表中选择 3多选)',
  `input_list` text DEFAULT NULL COMMENT '可选值列表，用逗号分隔',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `is_required` char(1) DEFAULT '0' COMMENT '是否必填(0否 1是)',
  `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  `description` varchar(500) DEFAULT NULL COMMENT '属性描述',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`attribute_id`),
  UNIQUE KEY `uk_attribute_name` (`tenant_id`, `attribute_name`),
  UNIQUE KEY `uk_attribute_code` (`tenant_id`, `attribute_code`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_attribute_type` (`attribute_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品属性表';

-- ----------------------------
-- 初始化商品属性数据
-- ----------------------------
INSERT INTO `product_attribute` VALUES 
(1, '000000', '颜色', 'color', '1', '2', '红色,蓝色,绿色,黑色,白色,黄色,紫色,粉色', 1, '1', '0', '商品颜色规格', 103, 1, NOW(), 1, NOW()),
(2, '000000', '尺寸', 'size', '1', '2', 'XS,S,M,L,XL,XXL,XXXL', 2, '1', '0', '商品尺寸规格', 103, 1, NOW(), 1, NOW()),
(3, '000000', '内存', 'memory', '1', '2', '64GB,128GB,256GB,512GB,1TB', 3, '1', '0', '设备内存规格', 103, 1, NOW(), 1, NOW()),
(4, '000000', '屏幕尺寸', 'screen_size', '2', '1', '', 4, '0', '0', '设备屏幕尺寸参数', 103, 1, NOW(), 1, NOW()),
(5, '000000', '重量', 'weight', '2', '1', '', 5, '0', '0', '商品重量参数', 103, 1, NOW(), 1, NOW()),
(6, '000000', '材质', 'material', '2', '2', '棉,麻,丝,毛,聚酯纤维,尼龙,皮革,金属,塑料,木材', 6, '0', '0', '商品材质参数', 103, 1, NOW(), 1, NOW()),
(7, '000000', '品牌型号', 'model', '2', '1', '', 7, '0', '0', '商品型号参数', 103, 1, NOW(), 1, NOW()),
(8, '000000', '保修期', 'warranty', '2', '2', '1年,2年,3年,5年,终身保修', 8, '0', '0', '商品保修期参数', 103, 1, NOW(), 1, NOW()),
(9, '000000', '产地', 'origin', '2', '2', '中国,美国,日本,德国,韩国,意大利,法国,英国', 9, '0', '0', '商品产地参数', 103, 1, NOW(), 1, NOW()),
(10, '000000', '适用年龄', 'age_range', '2', '2', '0-3岁,3-6岁,6-12岁,12-18岁,18岁以上', 10, '0', '0', '商品适用年龄参数', 103, 1, NOW(), 1, NOW());

-- ----------------------------
-- 字典数据：商品属性类型
-- ----------------------------
INSERT INTO sys_dict_type (dict_id, tenant_id, dict_name, dict_type, status, create_dept, create_by, create_time, update_by, update_time, remark) VALUES 
(100, '000000', '商品属性类型', 'product_attribute_type', '0', 103, 1, NOW(), 1, NOW(), '商品属性类型列表');

INSERT INTO sys_dict_data (dict_code, tenant_id, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_dept, create_by, create_time, update_by, update_time, remark) VALUES 
(100, '000000', 1, '规格', '1', 'product_attribute_type', '', 'primary', 'Y', '0', 103, 1, NOW(), 1, NOW(), '商品规格属性'),
(101, '000000', 2, '参数', '2', 'product_attribute_type', '', 'info', 'N', '0', 103, 1, NOW(), 1, NOW(), '商品参数属性');

-- ----------------------------
-- 字典数据：输入类型
-- ----------------------------
INSERT INTO sys_dict_type (dict_id, tenant_id, dict_name, dict_type, status, create_dept, create_by, create_time, update_by, update_time, remark) VALUES 
(101, '000000', '商品输入类型', 'product_input_type', '0', 103, 1, NOW(), 1, NOW(), '商品属性输入类型列表');

INSERT INTO sys_dict_data (dict_code, tenant_id, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_dept, create_by, create_time, update_by, update_time, remark) VALUES 
(102, '000000', 1, '手工录入', '1', 'product_input_type', '', 'primary', 'Y', '0', 103, 1, NOW(), 1, NOW(), '手工录入属性值'),
(103, '000000', 2, '从列表中选择', '2', 'product_input_type', '', 'success', 'N', '0', 103, 1, NOW(), 1, NOW(), '从预设列表中选择'),
(104, '000000', 3, '多选', '3', 'product_input_type', '', 'warning', 'N', '0', 103, 1, NOW(), 1, NOW(), '多选属性值');
