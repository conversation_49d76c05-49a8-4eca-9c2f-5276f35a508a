-- ----------------------------
-- 商品品牌表
-- ----------------------------
DROP TABLE IF EXISTS `product_brand`;
CREATE TABLE `product_brand` (
  `brand_id` bigint NOT NULL COMMENT '品牌ID',
  `tenant_id` varchar(20) DEFAULT '000000' COMMENT '租户编号',
  `brand_name` varchar(100) NOT NULL COMMENT '品牌名称',
  `brand_code` varchar(50) DEFAULT NULL COMMENT '品牌编码',
  `brand_logo` varchar(500) DEFAULT NULL COMMENT '品牌LOGO',
  `brand_website` varchar(200) DEFAULT NULL COMMENT '品牌官网',
  `brand_description` varchar(1000) DEFAULT NULL COMMENT '品牌描述',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  `is_recommend` char(1) DEFAULT '0' COMMENT '是否推荐(0否 1是)',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`brand_id`),
  UNIQUE KEY `uk_brand_name` (`tenant_id`, `brand_name`),
  UNIQUE KEY `uk_brand_code` (`tenant_id`, `brand_code`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_status` (`status`),
  KEY `idx_is_recommend` (`is_recommend`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品品牌表';

-- ----------------------------
-- 初始化商品品牌数据
-- ----------------------------
INSERT INTO `product_brand` VALUES 
(1, '000000', '苹果', 'APPLE', NULL, 'https://www.apple.com', '苹果公司，全球知名科技品牌', 1, '0', '1', 103, 1, NOW(), 1, NOW()),
(2, '000000', '华为', 'HUAWEI', NULL, 'https://www.huawei.com', '华为技术有限公司，全球领先的ICT基础设施和智能终端提供商', 2, '0', '1', 103, 1, NOW(), 1, NOW()),
(3, '000000', '小米', 'XIAOMI', NULL, 'https://www.mi.com', '小米科技有限责任公司，专注于高端智能手机、互联网电视以及智能家居生态链建设的创新型科技企业', 3, '0', '1', 103, 1, NOW(), 1, NOW()),
(4, '000000', '三星', 'SAMSUNG', NULL, 'https://www.samsung.com', '三星集团，韩国最大的跨国企业集团', 4, '0', '1', 103, 1, NOW(), 1, NOW()),
(5, '000000', 'OPPO', 'OPPO', NULL, 'https://www.oppo.com', 'OPPO广东移动通信有限公司，专注于智能终端产品的研发和制造', 5, '0', '1', 103, 1, NOW(), 1, NOW()),
(6, '000000', 'vivo', 'VIVO', NULL, 'https://www.vivo.com', 'vivo为一个专注于智能手机领域的手机品牌', 6, '0', '1', 103, 1, NOW(), 1, NOW()),
(7, '000000', '联想', 'LENOVO', NULL, 'https://www.lenovo.com', '联想集团有限公司，全球化的科技公司', 7, '0', '0', 103, 1, NOW(), 1, NOW()),
(8, '000000', '戴尔', 'DELL', NULL, 'https://www.dell.com', '戴尔科技集团，全球知名的科技公司', 8, '0', '0', 103, 1, NOW(), 1, NOW()),
(9, '000000', '惠普', 'HP', NULL, 'https://www.hp.com', '惠普公司，全球知名的信息科技公司', 9, '0', '0', 103, 1, NOW(), 1, NOW()),
(10, '000000', '索尼', 'SONY', NULL, 'https://www.sony.com', '索尼公司，日本知名的跨国企业集团', 10, '0', '0', 103, 1, NOW(), 1, NOW());
