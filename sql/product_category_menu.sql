-- ----------------------------
-- 商品管理菜单权限
-- ----------------------------

-- 商品管理主菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark) VALUES
(2000, '商品管理', 0, 4, 'product', NULL, '', 1, 0, 'M', '0', '0', '', 'shopping', 103, 1, NOW(), 1, NOW(), '商品管理目录');

-- 商品分类管理菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark) VALUES
(2001, '商品分类', 2000, 1, 'category', 'product/category/index', '', 1, 0, 'C', '0', '0', 'product:category:list', 'tree-table', 103, 1, NOW(), 1, NOW(), '商品分类菜单');

-- 商品分类按钮权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark) VALUES
(2002, '商品分类查询', 2001, 1, '', '', '', 1, 0, 'F', '0', '0', 'product:category:query', '#', 103, 1, NOW(), 1, NOW(), ''),
(2003, '商品分类新增', 2001, 2, '', '', '', 1, 0, 'F', '0', '0', 'product:category:add', '#', 103, 1, NOW(), 1, NOW(), ''),
(2004, '商品分类修改', 2001, 3, '', '', '', 1, 0, 'F', '0', '0', 'product:category:edit', '#', 103, 1, NOW(), 1, NOW(), ''),
(2005, '商品分类删除', 2001, 4, '', '', '', 1, 0, 'F', '0', '0', 'product:category:remove', '#', 103, 1, NOW(), 1, NOW(), ''),
(2006, '商品分类导出', 2001, 5, '', '', '', 1, 0, 'F', '0', '0', 'product:category:export', '#', 103, 1, NOW(), 1, NOW(), '');

-- 商品品牌管理菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark) VALUES
(2007, '商品品牌', 2000, 2, 'brand', 'product/brand/index', '', 1, 0, 'C', '0', '0', 'product:brand:list', 'star', 103, 1, NOW(), 1, NOW(), '商品品牌菜单');

-- 商品品牌按钮权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark) VALUES
(2008, '商品品牌查询', 2007, 1, '', '', '', 1, 0, 'F', '0', '0', 'product:brand:query', '#', 103, 1, NOW(), 1, NOW(), ''),
(2009, '商品品牌新增', 2007, 2, '', '', '', 1, 0, 'F', '0', '0', 'product:brand:add', '#', 103, 1, NOW(), 1, NOW(), ''),
(2010, '商品品牌修改', 2007, 3, '', '', '', 1, 0, 'F', '0', '0', 'product:brand:edit', '#', 103, 1, NOW(), 1, NOW(), ''),
(2011, '商品品牌删除', 2007, 4, '', '', '', 1, 0, 'F', '0', '0', 'product:brand:remove', '#', 103, 1, NOW(), 1, NOW(), ''),
(2012, '商品品牌导出', 2007, 5, '', '', '', 1, 0, 'F', '0', '0', 'product:brand:export', '#', 103, 1, NOW(), 1, NOW(), '');

-- 商品属性管理菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark) VALUES
(2013, '商品属性', 2000, 3, 'attribute', 'product/attribute/index', '', 1, 0, 'C', '0', '0', 'product:attribute:list', 'component', 103, 1, NOW(), 1, NOW(), '商品属性菜单');

-- 商品属性按钮权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark) VALUES
(2014, '商品属性查询', 2013, 1, '', '', '', 1, 0, 'F', '0', '0', 'product:attribute:query', '#', 103, 1, NOW(), 1, NOW(), ''),
(2015, '商品属性新增', 2013, 2, '', '', '', 1, 0, 'F', '0', '0', 'product:attribute:add', '#', 103, 1, NOW(), 1, NOW(), ''),
(2016, '商品属性修改', 2013, 3, '', '', '', 1, 0, 'F', '0', '0', 'product:attribute:edit', '#', 103, 1, NOW(), 1, NOW(), ''),
(2017, '商品属性删除', 2013, 4, '', '', '', 1, 0, 'F', '0', '0', 'product:attribute:remove', '#', 103, 1, NOW(), 1, NOW(), ''),
(2018, '商品属性导出', 2013, 5, '', '', '', 1, 0, 'F', '0', '0', 'product:attribute:export', '#', 103, 1, NOW(), 1, NOW(), '');
