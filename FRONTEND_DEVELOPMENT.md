# 商品管理前端开发文档

## 📋 开发概述

基于 **Vben Admin 5.5.7** 框架为商品管理模块开发了完整的前端管理界面，包括商品分类、商品品牌、商品属性三个核心功能模块。

## 🏗️ 技术架构

### 前端技术栈
- **Vue 3** + **TypeScript** - 现代化前端框架
- **Vben Admin 5.5.7** - 企业级管理后台框架
- **Ant Design Vue** - UI组件库
- **Vite** - 构建工具
- **Pinia** - 状态管理
- **Vue Router** - 路由管理
- **VXE Table** - 高性能表格组件

### 项目结构
```
ruoyi-plus-vben5/apps/web-antd/src/
├── api/product/                    # API接口定义
│   ├── category/                   # 商品分类API
│   ├── brand/                      # 商品品牌API
│   └── attribute/                  # 商品属性API
├── views/product/                  # 页面组件
│   ├── category/                   # 商品分类页面
│   ├── brand/                      # 商品品牌页面
│   └── attribute/                  # 商品属性页面
└── locales/langs/zh-CN/            # 国际化配置
    └── menu.json                   # 菜单国际化
```

### 动态路由配置
RuoYi-Vue-Plus 使用动态路由机制，前端路由配置通过后端接口获取，存储在 `sys_menu` 表中：

```sql
-- 一级菜单（目录）
INSERT INTO sys_menu VALUES (2000, '商品管理', 0, 3, 'product', NULL, '', 1, 0, 'M', '0', '0', '', 'shopping', 103, 1, NOW(), NULL, NULL, '商品管理目录');

-- 二级菜单（页面）
INSERT INTO sys_menu VALUES (2001, '商品分类', 2000, 1, 'category', 'product/category/index', '', 1, 0, 'C', '0', '0', 'product:category:list', 'tree', 103, 1, NOW(), NULL, NULL, '商品分类菜单');
```

## 🔧 已完成功能

### 1. 商品分类管理 (`/product/category`)
- **树形结构展示** - 支持无限级分类层次
- **CRUD操作** - 新增、编辑、删除分类
- **树形操作** - 展开/收起、拖拽排序
- **批量操作** - 批量删除、导出
- **搜索过滤** - 按分类名称、编码、状态筛选

**核心文件：**
- `api/product/category/` - API接口和类型定义
- `views/product/category/` - 页面组件和数据配置

### 2. 商品品牌管理 (`/product/brand`)
- **列表展示** - 分页表格展示品牌信息
- **CRUD操作** - 新增、编辑、删除品牌
- **图片展示** - 品牌LOGO图片预览
- **链接跳转** - 品牌官网链接
- **推荐标记** - 是否推荐品牌标识
- **批量操作** - 批量删除、导出

**核心文件：**
- `api/product/brand/` - API接口和类型定义
- `views/product/brand/` - 页面组件和数据配置

### 3. 商品属性管理 (`/product/attribute`)
- **属性类型** - 规格属性、参数属性
- **输入类型** - 文本框、下拉框、单选框、多选框、数字框
- **动态表单** - 根据输入类型动态显示可选值配置
- **必填标记** - 属性是否必填设置
- **批量操作** - 批量删除、导出

**核心文件：**
- `api/product/attribute/` - API接口和类型定义
- `views/product/attribute/` - 页面组件和数据配置

## 📡 API接口规范

### 接口命名规范
```typescript
// 查询列表
export function categoryList(params?: ProductCategoryQuery)
export function brandList(params?: ProductBrandQuery & PageQuery)
export function attributeList(params?: ProductAttributeQuery & PageQuery)

// 获取详情
export function categoryInfo(categoryId: ID)
export function brandInfo(brandId: ID)
export function attributeInfo(attributeId: ID)

// 新增
export function categoryAdd(data: ProductCategoryForm)
export function brandAdd(data: ProductBrandForm)
export function attributeAdd(data: ProductAttributeForm)

// 编辑
export function categoryEdit(data: ProductCategoryForm)
export function brandEdit(data: ProductBrandForm)
export function attributeEdit(data: ProductAttributeForm)

// 删除
export function categoryRemove(categoryIds: ID | ID[])
export function brandRemove(brandIds: ID | ID[])
export function attributeRemove(attributeIds: ID | ID[])

// 导出
export function categoryExport(params?: ProductCategoryQuery)
export function brandExport(params?: ProductBrandQuery)
export function attributeExport(params?: ProductAttributeQuery)
```

### 返回数据格式
```typescript
// 分页列表返回格式
interface PageResult<T> {
  rows: T[];
  total: number;
}

// 树形数据返回格式（分类）
interface ProductCategory {
  categoryId?: number;
  parentId?: number;
  categoryName: string;
  children?: ProductCategory[];
  // ...其他字段
}
```

## 🎨 组件设计模式

### 1. 页面组件结构
```vue
<template>
  <Page>
    <!-- 表格组件 -->
    <VbenVxeGrid ref="gridRef" v-bind="gridOptions">
      <!-- 工具栏操作 -->
      <template #toolbar-actions>
        <Button type="primary" @click="handleAdd">新增</Button>
        <Button danger @click="handleBatchDelete">删除</Button>
        <Button @click="handleExport">导出</Button>
      </template>
      
      <!-- 行操作 -->
      <template #action="{ row }">
        <Button @click="handleEdit(row)">编辑</Button>
        <Popconfirm @confirm="handleDelete(row)">
          <Button danger>删除</Button>
        </Popconfirm>
      </template>
    </VbenVxeGrid>

    <!-- 抽屉表单 -->
    <DrawerComponent ref="drawerRef" @success="handleSuccess" />
  </Page>
</template>
```

### 2. 数据配置分离
```typescript
// data.ts - 配置文件
export const querySchema: FormSchemaGetter = () => [...]
export const columns: VxeGridProps['columns'] = [...]
export const formSchema: FormSchemaGetter = (params) => [...]
```

### 3. 抽屉表单组件
```vue
<template>
  <VbenDrawer v-model:open="drawerState.visible">
    <VbenForm ref="formRef" :schema="formSchema" />
    <template #footer>
      <Button @click="handleClose">取消</Button>
      <Button type="primary" @click="handleSubmit">确定</Button>
    </template>
  </VbenDrawer>
</template>
```

## 🔧 字典数据配置

### 新增字典类型
```typescript
// packages/@core/base/shared/src/constants/dict-enum.ts
export const DictEnum = {
  // ...现有字典
  PRODUCT_ATTRIBUTE_TYPE: 'product_attribute_type', // 商品属性类型
  PRODUCT_INPUT_TYPE: 'product_input_type', // 商品输入类型
} as const;
```

### 字典数据SQL
```sql
-- 字典类型
INSERT INTO sys_dict_type VALUES (100, '000000', '商品属性类型', 'product_attribute_type', '0', 103, 1, NOW(), NULL, NULL, '商品属性类型列表');
INSERT INTO sys_dict_type VALUES (101, '000000', '商品输入类型', 'product_input_type', '0', 103, 1, NOW(), NULL, NULL, '商品输入类型列表');

-- 字典数据
INSERT INTO sys_dict_data VALUES (1000, '000000', 1, '规格', '1', 'product_attribute_type', '', 'primary', 'Y', 103, 1, NOW(), NULL, NULL, '商品规格属性');
-- ...更多字典数据
```

## 🌐 国际化配置

### 菜单国际化
```json
// locales/langs/zh-CN/menu.json
{
  "product": {
    "root": "商品管理",
    "category": "商品分类",
    "brand": "商品品牌",
    "attribute": "商品属性"
  }
}
```

## 🚀 部署说明

### 1. 执行数据库脚本
```sql
-- 执行菜单和字典配置
source sql/product_dict.sql
```

### 2. 安装依赖
```bash
cd ruoyi-plus-vben5
pnpm install
```

### 3. 开发环境启动
```bash
cd apps/web-antd
pnpm dev
```

### 4. 生产环境构建
```bash
cd apps/web-antd
pnpm build
```

## 📝 开发规范

### 1. 文件命名
- API文件：`kebab-case` (如：`product-category.ts`)
- 组件文件：`PascalCase` (如：`CategoryDrawer.vue`)
- 页面文件：`index.vue`

### 2. 类型定义
- 实体类型：`ProductCategory`
- 查询类型：`ProductCategoryQuery`
- 表单类型：`ProductCategoryForm`

### 3. 函数命名
- 查询：`list`、`info`、`tree`
- 操作：`add`、`edit`、`remove`
- 导出：`export`

## 🔄 后续开发计划

### 待开发模块
1. **SPU管理** - 标准产品单元
2. **SKU管理** - 库存量单位
3. **商品库存** - 库存管理
4. **价格管理** - 价格策略

### 功能增强
1. **图片上传** - 集成OSS文件上传
2. **富文本编辑** - 商品详情编辑
3. **批量导入** - Excel批量导入商品
4. **数据统计** - 商品数据分析图表

## 🐛 已知问题

1. **图片上传** - 暂未集成文件上传功能，品牌LOGO上传需要后续开发
2. **富文本编辑** - 商品详情编辑器需要后续集成
3. **批量导入** - Excel批量导入功能需要后续开发

## 📞 技术支持

如有问题，请参考：
1. [Vben Admin 官方文档](https://doc.vben.pro/)
2. [Ant Design Vue 文档](https://antdv.com/)
3. [Vue 3 官方文档](https://cn.vuejs.org/)
